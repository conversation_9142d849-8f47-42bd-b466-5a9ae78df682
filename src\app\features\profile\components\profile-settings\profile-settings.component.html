<!-- Profile Settings Content -->
<div class="max-w-6xl">

  <!-- Loading State -->
  <div *ngIf="isLoading" class="flex justify-center items-center py-20">
    <div class="bg-slate-800/60 backdrop-blur-md border border-slate-600/30 rounded-xl p-6">
      <svg class="animate-spin h-8 w-8 text-blue-400" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
        <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
        <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
      </svg>
    </div>
  </div>

  <!-- Error State -->
  <div *ngIf="errorMessage && !isLoading" class="bg-red-500/20 border border-red-500/50 rounded-lg p-4 text-center mb-4">
    <p class="text-sm lg:text-base text-red-300 mb-3">{{ errorMessage }}</p>
    <button
      (click)="loadUserProfile()"
      class="px-3 py-1.5 text-sm lg:text-base bg-red-600 hover:bg-red-700 text-white rounded-md transition-colors"
    >
      Попробовать снова
    </button>
  </div>

  <!-- Profile Content -->
  <div *ngIf="userProfile && !isLoading && !errorMessage">
  <!-- Header with User Icon and Title -->
  <div class="flex items-center mb-4 lg:mb-6">
    <div class="w-10 h-10 lg:w-12 lg:h-12 bg-gradient-to-br from-blue-600 to-slate-700 rounded-full flex items-center justify-center mr-3 lg:mr-4">
      <svg class="w-5 h-5 lg:w-6 lg:h-6 text-white" fill="currentColor" viewBox="0 0 20 20">
        <path fill-rule="evenodd" d="M10 9a3 3 0 100-6 3 3 0 000 6zm-7 9a7 7 0 1114 0H3z" clip-rule="evenodd"></path>
      </svg>
    </div>
    <div>
      <h1 class="text-xl lg:text-2xl font-bold text-white mb-1">Параметры</h1>
      <p class="text-sm lg:text-base text-gray-300">Управляйте данными своей учетной записи</p>
    </div>
  </div>

  <!-- Account Information Section -->
  <div class="bg-slate-900/60 backdrop-blur-sm border border-slate-700/40 rounded-lg p-4 lg:p-6 mb-4 lg:mb-6 profile-section">
    <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between mb-4 gap-2">
      <h3 class="text-base lg:text-lg font-semibold text-white">Информация об аккаунте</h3>
      <!-- User Code Badge -->
      <span class="inline-flex px-2 py-1 text-xs font-semibold rounded bg-blue-900/50 text-blue-400 border border-blue-500/50 self-start sm:self-auto">
        {{ userProfile.user_code }}
      </span>
    </div>

    <div class="grid grid-cols-1 lg:grid-cols-2 gap-4">
      <!-- Email Field -->
      <div>
        <label class="block text-sm lg:text-base font-medium text-gray-300 mb-1">Email</label>
        <div class="relative">
          <input
            type="email"
            [value]="userProfile.email"
            readonly
            class="w-full px-3 py-2 lg:py-2.5 text-sm lg:text-base bg-slate-700/30 border border-slate-600/30 rounded-md text-white cursor-not-allowed"
          />
          <div class="absolute inset-y-0 right-0 flex items-center pr-2 lg:pr-3">
            <svg class="w-4 h-4 lg:w-5 lg:h-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"></path>
            </svg>
          </div>
        </div>
      </div>

      <!-- Phone Field -->
      <div>
        <div class="flex items-center justify-between mb-1">
          <label class="block text-sm lg:text-base font-medium text-gray-300">Телефон</label>
          <button
            *ngIf="!isEditingPhone"
            (click)="togglePhoneEdit()"
            class="text-sm lg:text-base text-blue-400 hover:text-blue-300 transition-colors"
          >
            Изменить
          </button>
        </div>
        <form [formGroup]="phoneForm" (ngSubmit)="onUpdatePhone()" *ngIf="isEditingPhone; else phoneDisplay">
          <div class="flex flex-col sm:flex-row space-y-2 sm:space-y-0 sm:space-x-2">
            <input
              type="tel"
              formControlName="phone"
              placeholder="Номер телефона"
              class="flex-1 px-3 py-2 lg:py-2.5 text-sm lg:text-base bg-slate-800/60 border border-slate-600/50 rounded-md text-white placeholder-gray-400 focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-transparent"
            />
            <button
              type="submit"
              [disabled]="savingPhone"
              class="px-3 lg:px-4 py-2 lg:py-2.5 text-sm lg:text-base bg-blue-600 hover:bg-blue-700 text-white rounded-md transition-colors disabled:opacity-50 min-h-[44px] sm:min-h-auto"
            >
              <span *ngIf="savingPhone" class="inline-block">
                <svg class="animate-spin h-3 w-3" fill="none" viewBox="0 0 24 24">
                  <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                  <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                </svg>
              </span>
              <span *ngIf="!savingPhone">✓</span>
            </button>
            <button
              type="button"
              (click)="togglePhoneEdit()"
              [disabled]="savingPhone"
              class="px-2 py-2 text-sm lg:text-base bg-gray-600 hover:bg-gray-700 text-white rounded-md transition-colors disabled:opacity-50"
            >
              ✕
            </button>
          </div>
        </form>
        <ng-template #phoneDisplay>
          <div class="relative">
            <input
              type="tel"
              [value]="userProfile.phone || 'Не указан'"
              readonly
              class="w-full px-3 py-2 text-sm lg:text-base bg-slate-700/30 border border-slate-600/30 rounded-md text-white cursor-not-allowed"
            />
            <div class="absolute inset-y-0 right-0 flex items-center pr-2">
              <svg class="w-4 h-4 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z"></path>
              </svg>
            </div>
          </div>
        </ng-template>
      </div>
    </div>

  </div>

  <!-- Password Change Section -->
  <div class="bg-slate-900/60 backdrop-blur-sm border border-slate-700/40 rounded-lg p-6 mb-6 profile-section">
    <div class="flex items-center justify-between mb-4">
      <h3 class="text-lg font-semibold text-white">Изменение пароля</h3>
      <button
        *ngIf="!isEditingPassword"
        (click)="togglePasswordEdit()"
        class="px-3 py-1.5 text-sm lg:text-base bg-blue-600 hover:bg-blue-700 text-white rounded-md transition-colors"
      >
        Изменить пароль
      </button>
    </div>

    <div *ngIf="isEditingPassword">
      <form [formGroup]="passwordForm" (ngSubmit)="onChangePassword()">
        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
          <!-- Current Password -->
          <div>
            <label class="block text-sm lg:text-base font-medium text-gray-300 mb-1">Текущий пароль</label>
            <input
              type="password"
              formControlName="old_password"
              placeholder="Введите текущий пароль"
              class="w-full px-3 py-2 text-sm lg:text-base bg-slate-800/60 border border-slate-600/50 rounded-md text-white placeholder-gray-400 focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-transparent"
            />
            <div *ngIf="passwordForm.get('old_password')?.invalid && passwordForm.get('old_password')?.touched" class="mt-1 text-red-400 text-sm lg:text-base">
              Текущий пароль обязателен
            </div>
          </div>

          <!-- New Password -->
          <div>
            <label class="block text-sm lg:text-base font-medium text-gray-300 mb-1">Новый пароль</label>
            <input
              type="password"
              formControlName="new_password"
              placeholder="Введите новый пароль"
              class="w-full px-3 py-2 text-sm lg:text-base bg-slate-800/60 border border-slate-600/50 rounded-md text-white placeholder-gray-400 focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-transparent"
            />
            <div *ngIf="passwordForm.get('new_password')?.invalid && passwordForm.get('new_password')?.touched" class="mt-1 text-red-400 text-sm lg:text-base">
              Новый пароль должен содержать минимум 6 символов
            </div>
          </div>
        </div>

        <!-- Form Actions -->
        <div class="flex justify-end space-x-2 mt-4">
          <button
            type="button"
            (click)="togglePasswordEdit()"
            [disabled]="savingPassword"
            class="px-3 py-1.5 text-sm lg:text-base bg-gray-600 hover:bg-gray-700 text-white rounded-md transition-colors disabled:opacity-50"
          >
            Отмена
          </button>
          <button
            type="submit"
            [disabled]="passwordForm.invalid || savingPassword"
            class="px-3 py-1.5 text-sm lg:text-base bg-blue-600 hover:bg-blue-700 text-white rounded-md transition-colors disabled:opacity-50 flex items-center"
          >
            <span *ngIf="savingPassword" class="mr-1.5">
              <svg class="animate-spin h-3 w-3" fill="none" viewBox="0 0 24 24">
                <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
              </svg>
            </span>
            {{ savingPassword ? 'Сохранение...' : 'Изменить пароль' }}
          </button>
        </div>
      </form>
    </div>

    <div *ngIf="!isEditingPassword" class="text-sm lg:text-base text-gray-400">
      Нажмите "Изменить пароль" для обновления вашего пароля
    </div>
  </div>

  <!-- Account Status Section -->
  <div class="bg-slate-900/60 backdrop-blur-sm border border-slate-700/40 rounded-lg p-4 profile-section">
    <h3 class="text-lg font-semibold text-white mb-3">Статус аккаунта</h3>
    <div class="space-y-2">
      <div class="flex justify-between items-center">
        <span class="text-sm text-green-300 uppercase font-bold">Код пользователя:</span>
        <span class="text-sm text-green-300 font-medium">{{ userProfile.user_code }}</span>
      </div>
      <div class="flex justify-between items-center">
        <span class="text-sm text-gray-300">ID пользователя:</span>
        <span class="text-sm text-white font-medium">{{ userProfile.id }}</span>
      </div>
      <div class="flex justify-between items-center">
        <span class="text-sm text-gray-300">Статус:</span>
        <span [ngClass]="userProfile.is_staff ? 'text-green-400' : 'text-blue-400'" class="text-sm font-medium">
          {{ userProfile.is_staff ? 'Администратор' : 'Пользователь' }}
        </span>
      </div>
    </div>
  </div>

  <!-- Action Buttons -->
  <!-- div class="flex flex-col sm:flex-row gap-3">
    <button
      (click)="verifyToken()"
      class="px-4 py-2 text-sm bg-blue-600 hover:bg-blue-700 text-white rounded-md transition-colors font-medium"
    >
      Проверить токен
    </button>
    <button
      (click)="onRefreshProfile()"
      class="px-4 py-2 text-sm bg-green-600 hover:bg-green-700 text-white rounded-md transition-colors font-medium"
    >
      Обновить профиль
    </button>
    <button
      (click)="onLogout()"
      class="px-4 py-2 text-sm bg-red-600 hover:bg-red-700 text-white rounded-md transition-colors font-medium"
    >
      Выйти
    </button>
  </div>< -->

  </div> <!-- Close profile content div -->
</div>
